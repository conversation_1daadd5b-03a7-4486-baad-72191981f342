import builtins
from typing import Any

class MarketInfo:
    def model_dump(self) -> builtins.dict[str, Any]: ...
    def dict(self) -> builtins.dict[str, Any]: ...

class MarketActivity:
    def model_dump(self) -> builtins.dict[str, Any]: ...
    def dict(self) -> builtins.dict[str, Any]: ...

class RecentTrades:
    def model_dump(self) -> builtins.dict[str, Any]: ...
    def dict(self) -> builtins.dict[str, Any]: ...

class Fee:
    def __init__(
        self,
        ticker: str,
        name: str,
        maker: str,
        taker: str,
        deposit: str,
        withdraw: str,
        is_depositable: bool,
        is_withdrawal: bool,
        is_api_withdrawal: bool,
        is_api_depositable: bool,
    ) -> None: ...
    def model_dump(self) -> builtins.dict[str, Any]: ...
    def dict(self) -> builtins.dict[str, Any]: ...

class AssetStatus:
    def model_dump(self) -> builtins.dict[str, Any]: ...
    def dict(self) -> builtins.dict[str, Any]: ...

class Kline:
    def model_dump(self) -> builtins.dict[str, Any]: ...
    def dict(self) -> builtins.dict[str, Any]: ...

class MarketSingleResponse:
    def model_dump(self) -> builtins.dict[str, Any]: ...
    def dict(self) -> builtins.dict[str, Any]: ...

class Tickers:
    def __len__(self) -> int: ...
    def model_dump(self) -> builtins.dict[str, Any]: ...
    def dict(self) -> builtins.dict[str, Any]: ...
