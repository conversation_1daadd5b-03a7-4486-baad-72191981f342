[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "aiowhitebit-mcp"
version = "0.2.7"
description = "MCP server and client for WhiteBit cryptocurrency exchange API"
readme = "README.md"
authors = [
    {name = "doubledare704", email = "<EMAIL>"}
]
license = {text = "Apache License 2.0"}
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: Apache Software License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Internet :: WWW/HTTP",
]
keywords = ["whitebit", "cryptocurrency", "exchange", "api", "mcp", "claude"]
requires-python = ">=3.10"
dependencies = [
    "aiowhitebit==0.2.5",
    "fastmcp==2.10.2",
    "pydantic>=2.11.7",
    "aiohttp>=3.12.13",
]

[project.optional-dependencies]
dev = [
    "pytest>=8.4.0",
    "pytest-asyncio>=1.0.0",
    "pytest-cov>=6.2.1",
    "ruff>=0.11.13",
    "pyright>=1.1.402",
    "pre-commit>=4.2.0",
]

[project.urls]
"Homepage" = "https://github.com/doubledare704/aiowhitebit-mcp"
"Bug Tracker" = "https://github.com/doubledare704/aiowhitebit-mcp/issues"
"Documentation" = "https://github.com/doubledare704/aiowhitebit-mcp#readme"
"Source Code" = "https://github.com/doubledare704/aiowhitebit-mcp"

[project.scripts]
aiowhitebit-mcp = "aiowhitebit_mcp.cli:main"

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_functions = "test_*"
asyncio_mode = "auto"
asyncio_default_fixture_loop_scope = "function"
markers = [
    "asyncio: mark a test as an asyncio test",
    "slow: mark test as slow",
    "integration: mark test as integration test",
]

[tool.ruff]
line-length = 120
target-version = "py310"

[tool.ruff.lint]
select = [
    "E",    # pycodestyle errors
    "W",    # pycodestyle warnings
    "F",    # pyflakes
    "I",    # isort
    "D",    # pydocstyle
    "UP",   # pyupgrade
    "N",    # pep8-naming
    "B",    # flake8-bugbear
    "C4",   # flake8-comprehensions
    "SIM",  # flake8-simplify
    "TCH",  # flake8-type-checking
]

[tool.ruff.lint.pydocstyle]
convention = "google"

[tool.ruff.lint.isort]
known-first-party = ["aiowhitebit_mcp"]

[tool.pyright]
include = ["src"]
exclude = ["**/node_modules", "**/__pycache__", "exzample/**", "tests/**"]
typeCheckingMode = "basic"
pythonVersion = "3.10"
stubPath = "typings"
venvPath = "."
venv = ".venv"
